<template>
  <div class="admin-layout">
    <!-- 移动端菜单按钮 -->
    <div class="admin-mobile-nav-toggle" @click="mobileMenuOpen = true">
      <i class="el-icon-menu"></i>
    </div>
    <!-- 移动端抽屉菜单和遮罩层 -->
    <div v-if="mobileMenuOpen" class="admin-mobile-nav-overlay" @click="mobileMenuOpen = false"></div>
    <div v-if="mobileMenuOpen" class="admin-mobile-nav-drawer">
      <div class="admin-mobile-nav-header">
        <img src="@/assets/logo.png" alt="Logo" class="admin-mobile-nav-logo">
        <h3 class="admin-mobile-nav-title">管理控制台</h3>
        <div class="admin-mobile-nav-close" @click="mobileMenuOpen = false">
          <i class="el-icon-close"></i>
        </div>
      </div>

      <!-- 用户信息区域 -->
      <div class="admin-mobile-user-info">
        <div class="user-avatar">
          <i class="el-icon-user"></i>
        </div>
        <div class="user-details">
          <div class="user-name">{{ displayUsername }}</div>
          <div class="user-role">管理员</div>
        </div>
      </div>

      <el-menu
        :default-active="activeMenu"
        class="admin-mobile-nav-list"
        background-color="#304156"
        text-color="#bfcbd9"
        active-text-color="#409EFF"
        @select="handleMobileMenuSelect"
      >
        <el-menu-item index="/admin/dashboard">
          <i class="el-icon-s-home"></i>
          <span>控制台</span>
        </el-menu-item>
        <el-submenu index="equipment">
          <template slot="title">
            <i class="el-icon-s-grid"></i>
            <span>设备管理</span>
          </template>
          <el-menu-item index="/admin/equipment">
            <i class="el-icon-s-management"></i>
            <span>设备列表</span>
          </el-menu-item>
          <el-menu-item index="/admin/category">
            <i class="el-icon-collection-tag"></i>
            <span>设备类别</span>
          </el-menu-item>
        </el-submenu>
        <el-menu-item index="/admin/reservation">
          <i class="el-icon-s-order"></i>
          <span>预定管理</span>
        </el-menu-item>
        <el-menu-item index="/admin/announcement">
          <i class="el-icon-message-solid"></i>
          <span>公告管理</span>
        </el-menu-item>
        <el-submenu index="email-mobile">
          <template slot="title">
            <i class="el-icon-message"></i>
            <span>邮件管理</span>
          </template>
          <el-menu-item index="/admin/email/settings">
            <i class="el-icon-setting"></i>
            <span>邮件设置</span>
          </el-menu-item>
          <el-menu-item index="/admin/email/templates">
            <i class="el-icon-document"></i>
            <span>邮件模板</span>
          </el-menu-item>
          <el-menu-item index="/admin/email/logs">
            <i class="el-icon-tickets"></i>
            <span>邮件日志</span>
          </el-menu-item>
        </el-submenu>
        <el-menu-item index="/admin/db-viewer">
          <i class="el-icon-view"></i>
          <span>数据库表查看</span>
        </el-menu-item>
        <el-menu-item index="/admin/system-logs">
          <i class="el-icon-document"></i>
          <span>系统日志</span>
        </el-menu-item>
        <el-menu-item index="/admin/accounts">
          <i class="el-icon-user"></i>
          <span>账号管理</span>
        </el-menu-item>
      </el-menu>

      <!-- 底部操作按钮 -->
      <div class="admin-mobile-nav-footer">
        <div class="admin-mobile-nav-actions">
          <el-button
            type="primary"
            plain
            icon="el-icon-s-home"
            size="small"
            @click="handleMobileCommand('home')"
            class="mobile-action-btn"
          >
            返回首页
          </el-button>
          <el-button
            type="danger"
            plain
            icon="el-icon-switch-button"
            size="small"
            @click="handleMobileCommand('logout')"
            class="mobile-action-btn"
          >
            退出登录
          </el-button>
        </div>
      </div>
    </div>
    <el-container class="admin-container">
      <el-header class="admin-header">
        <div class="header-left">
          <img src="@/assets/logo.png" alt="Logo" class="header-logo" />
          <span class="header-title">管理控制台</span>
        </div>

        <div class="header-menu">
          <el-menu
            :default-active="activeMenu"
            class="top-menu"
            mode="horizontal"
            background-color="#FFFFFF"
            text-color="#333333"
            active-text-color="#409EFF"
            router
          >
            <el-menu-item index="/admin/dashboard">
              <i class="el-icon-s-home"></i>
              <span>控制台</span>
            </el-menu-item>

            <el-submenu index="equipment">
              <template slot="title">
                <i class="el-icon-s-grid"></i>
                <span>设备管理</span>
              </template>
              <el-menu-item index="/admin/equipment">
                <i class="el-icon-s-management"></i>
                <span>设备列表</span>
              </el-menu-item>
              <el-menu-item index="/admin/category">
                <i class="el-icon-collection-tag"></i>
                <span>设备类别</span>
              </el-menu-item>
            </el-submenu>

            <el-menu-item index="/admin/reservation">
              <i class="el-icon-s-order"></i>
              <span>预定管理</span>
            </el-menu-item>

            <el-menu-item index="/admin/announcement">
              <i class="el-icon-message-solid"></i>
              <span>公告管理</span>
            </el-menu-item>

            <el-submenu index="email">
              <template slot="title">
                <i class="el-icon-message"></i>
                <span>邮件管理</span>
              </template>
              <el-menu-item index="/admin/email/settings">
                <i class="el-icon-setting"></i>
                <span>邮件设置</span>
              </el-menu-item>
              <el-menu-item index="/admin/email/templates">
                <i class="el-icon-document"></i>
                <span>邮件模板</span>
              </el-menu-item>
              <el-menu-item index="/admin/email/logs">
                <i class="el-icon-tickets"></i>
                <span>邮件日志</span>
              </el-menu-item>
            </el-submenu>

            <el-menu-item index="/admin/db-viewer">
              <i class="el-icon-view"></i>
              <span>数据库表查看</span>
            </el-menu-item>

            <el-menu-item index="/admin/system-logs">
              <i class="el-icon-document"></i>
              <span>系统日志</span>
            </el-menu-item>

            <el-menu-item index="/admin/accounts">
              <i class="el-icon-user"></i>
              <span>账号管理</span>
            </el-menu-item>
          </el-menu>
        </div>

        <div class="header-right">
          <div class="user-info">
            <i class="el-icon-user"></i>
            <span>{{ displayUsername }}</span>
          </div>


          <el-button type="primary" plain icon="el-icon-s-home" size="small" @click="handleCommand('home')" class="home-btn">返回首页</el-button>
          <el-button type="danger" plain icon="el-icon-switch-button" size="small" @click="handleCommand('logout')" class="logout-btn">退出登录</el-button>
        </div>
      </el-header>

      <el-main class="admin-main">
        <keep-alive>
          <router-view></router-view>
        </keep-alive>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { updatePageTitle } from '@/router/permission'

export default {
  name: 'AdminLayout',

  data() {
    return {
      mobileMenuOpen: false
    }
  },

  computed: {
    ...mapGetters(['currentUser', 'getLanguage']),

    activeMenu() {
      return this.$route.path
    },

    currentLanguage() {
      return this.getLanguage
    },

    displayUsername() {
      // 优先显示用户名，如果没有则显示'管理员'
      return this.currentUser && this.currentUser.username ? this.currentUser.username : '管理员'
    },

    isSuperAdmin() {
      return this.currentUser && this.currentUser.role === 'superadmin'
    }
  },

  methods: {
    ...mapActions(['logout', 'setLanguage']),

    handleCommand(command) {
      if (command === 'logout') {
        this.handleLogout()
      } else if (command === 'home') {
        this.$router.push('/')
      }
    },

    handleLogout() {
      this.logout()
      this.$message.success('退出登录成功')
      this.$router.push('/admin/login')
    },

    handleLanguageChange(lang) {
      this.setLanguage(lang)
      this.$i18n.locale = lang

      // 更新页面标题
      setTimeout(() => {
        updatePageTitle()
      }, 0)
    },

    handleMobileMenuSelect(key) {
      this.$router.push(key)
      this.mobileMenuOpen = false
    },

    handleMobileCommand(command) {
      if (command === 'logout') {
        this.handleLogout()
      } else if (command === 'home') {
        this.$router.push('/')
      }
      this.mobileMenuOpen = false
    },

    toggleMobileMenu() {
      // 这里可以实现折叠菜单的逻辑，或 emit 事件给父组件
      const menu = document.querySelector('.header-menu')
      if (menu) {
        menu.style.display = (menu.style.display === 'none') ? '' : 'none'
      }
    }
  }
}
</script>

<style>
.admin-layout {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  display: flex;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
}

.admin-container {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

.admin-header,
.el-header {
  background-color: #FFFFFF !important;
  color: #333333 !important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  height: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid #EBEEF5;
}

.header-left {
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.header-logo {
  height: 32px;
  margin-right: 10px;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  color: #409EFF;
  white-space: nowrap;
}

.header-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.top-menu {
  background-color: #FFFFFF !important;
  border-bottom: none;
}

.top-menu .el-menu-item {
  height: 60px;
  line-height: 60px;
}

.top-menu .el-submenu__title {
  height: 60px;
  line-height: 60px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 0 20px;
}

.language-switcher {
  display: flex;
  align-items: center;
  margin-right: 15px;
}

.lang-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px 8px;
  color: #606266;
  font-size: 14px;
}

.lang-btn.active {
  color: #409EFF;
  font-weight: bold;
}

.divider {
  color: #DCDFE6;
  margin: 0 5px;
  opacity: 0.7;
}

.user-info {
  display: flex;
  align-items: center;
  color: #606266;
  margin-right: 15px;
  background-color: #F5F7FA;
  padding: 5px 12px;
  border-radius: 4px;
  font-size: 14px;
  border: 1px solid #DCDFE6;
}

.user-info i {
  margin-right: 5px;
}

.header-right .el-button {
  margin-left: 0;
  white-space: nowrap;
}

.admin-main {
  background-color: #f0f2f5;
  padding: 20px;
  overflow-y: auto;
  height: calc(100vh - 60px);
  width: 100%;
  max-width: 100%;
}

@media (max-width: 768px) {
  .admin-layout {
    position: relative;
    top: 0;
    left: 0;
  }

  .admin-header,
  .el-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    width: 100%;
    height: 60px;
  }

  .admin-main {
    margin-top: 60px;
    height: calc(100vh - 60px);
    padding-top: 20px;
  }

  /* 完全隐藏移动端的顶部菜单栏 */
  .admin-header,
  .el-header {
    display: none !important;
  }

  .admin-main {
    margin-top: 0 !important;
    height: 100vh !important;
    padding-top: 20px;
  }

  .admin-mobile-nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.5);
    z-index: 2999;
  }
  .admin-mobile-nav-drawer {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: #304156;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    transition: right 0.3s ease;
    box-shadow: -2px 0 8px rgba(0,0,0,0.1);
    overflow: hidden;
  }
  .admin-mobile-nav-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    background-color: #263445;
    position: relative;
  }

  .admin-mobile-nav-logo {
    width: 32px;
    height: 32px;
    margin-right: 10px;
  }

  .admin-mobile-nav-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    flex: 1;
  }

  .admin-mobile-nav-close {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #bfcbd9;
    border-radius: 4px;
    transition: all 0.3s ease;
  }

  .admin-mobile-nav-close:hover {
    background-color: #1f2d3d;
    color: #fff;
  }

  .admin-mobile-user-info {
    padding: 20px;
    background-color: #263445;
    border-bottom: 1px solid #1f2d3d;
    display: flex;
    align-items: center;
  }

  .user-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #409EFF;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    color: #fff;
    font-size: 20px;
  }

  .user-details {
    flex: 1;
  }

  .user-name {
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    margin-bottom: 4px;
  }

  .user-role {
    font-size: 12px;
    color: #bfcbd9;
  }
  .admin-mobile-nav-list {
    flex: 1;
    border-right: none;
    background: #304156;
    overflow-y: auto;
    max-height: calc(100vh - 200px); /* 为header和footer留出空间 */
  }
  .admin-mobile-nav-footer {
    padding: 15px 20px;
    background-color: #263445;
    border-top: 1px solid #1f2d3d;
    flex-shrink: 0; /* 防止被压缩 */
    margin-top: auto; /* 推到底部 */
  }

  .admin-mobile-nav-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .mobile-action-btn {
    width: 100% !important;
    margin: 0 !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
  }

  .mobile-action-btn.el-button--primary {
    background-color: transparent !important;
    border-color: #409EFF !important;
    color: #409EFF !important;
  }

  .mobile-action-btn.el-button--primary:hover {
    background-color: #409EFF !important;
    color: #fff !important;
  }

  .mobile-action-btn.el-button--danger {
    background-color: transparent !important;
    border-color: #F56C6C !important;
    color: #F56C6C !important;
  }

  .mobile-action-btn.el-button--danger:hover {
    background-color: #F56C6C !important;
    color: #fff !important;
  }
  .admin-mobile-nav-toggle {
    position: fixed;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #409EFF;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 3001;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
    transition: all 0.3s ease;
    font-size: 18px;
  }

  .admin-mobile-nav-toggle:hover {
    background-color: #337ecc;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
  }

  .admin-mobile-nav-toggle:active {
    transform: translateY(-50%) scale(0.95);
  }
}
</style>
